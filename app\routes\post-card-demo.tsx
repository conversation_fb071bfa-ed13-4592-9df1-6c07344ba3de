import { PostCard } from '~/components/post/PostCard';
import { PostCardCompact } from '~/components/post/PostCardCompact';
import type { Post } from '~/types';

// Mock data for demo
const mockPost: Post = {
  id: '1',
  title: 'Hướng dẫn thiết kế UI/UX hiện đại với React và Tailwind CSS',
  slug: 'huong-dan-thiet-ke-ui-ux-hien-dai',
  content: 'Trong bài viết này, chúng ta sẽ tìm hiểu cách thiết kế giao diện người dùng hiện đại và responsive với React và Tailwind CSS. Bài viết bao gồm các best practices, tips và tricks để tạo ra những component đẹp mắt và dễ sử dụng.',
  contentType: 'RICHTEXT',
  summary: 'Tìm hiểu cách thiết kế UI/UX hiện đại với React và Tailwind CSS',
  thumbnail: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop',
  thumbnailUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop',
  featured: true,
  published: true,
  status: 'PUBLISHED',
  user: {
    id: 'user1',
    username: 'designer_pro',
    email: '<EMAIL>',
    roles: ['ROLE_AUTHOR'],
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  },
  categories: [{
    id: 1,
    category: 'Design',
    slug: 'design',
    backgroundColor: '#8B5CF6',
    description: 'Design and UI/UX articles'
  }],
  tags: [
    { uuid: 'tag1', name: 'React', slug: 'react', description: 'React framework', color: '#61DAFB' },
    { uuid: 'tag2', name: 'Tailwind', slug: 'tailwind', description: 'Tailwind CSS', color: '#06B6D4' },
    { uuid: 'tag3', name: 'UI/UX', slug: 'ui-ux', description: 'User Interface and Experience', color: '#F59E0B' },
    { uuid: 'tag4', name: 'Frontend', slug: 'frontend', description: 'Frontend development', color: '#10B981' },
    { uuid: 'tag5', name: 'CSS', slug: 'css', description: 'Cascading Style Sheets', color: '#EF4444' }
  ],
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-15T10:30:00Z',
  commentCount: 24,
  viewCount: 1250,
  likeCount: 89,
  averageRating: 4.5,
  isLikedByCurrentUser: false,
  isSavedByCurrentUser: true,
  userRating: null,
  comments: []
};

const mockPostNoImage: Post = {
  ...mockPost,
  id: '2',
  title: 'Tối ưu hóa hiệu suất React App',
  thumbnail: undefined,
  thumbnailUrl: undefined,
  featured: false,
  categories: [{
    id: 2,
    category: 'Performance',
    slug: 'performance',
    backgroundColor: '#EF4444',
    description: 'Performance optimization'
  }],
  tags: [
    { uuid: 'tag1', name: 'React', slug: 'react', description: 'React framework', color: '#61DAFB' },
    { uuid: 'tag6', name: 'Performance', slug: 'performance', description: 'Performance optimization', color: '#F97316' }
  ],
  viewCount: 890,
  likeCount: 45,
  commentCount: 12,
  isSavedByCurrentUser: false
};

export default function PostCardDemo() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            PostCard Design Variants
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            So sánh các thiết kế PostCard khác nhau - từ đầy đủ đến compact
          </p>
        </div>

        {/* Original PostCard */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            1. PostCard Gốc (Đã tối ưu)
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Thiết kế card đã được tối ưu: giảm padding, gộp thông tin, compact hơn
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <PostCard post={mockPost} />
            <PostCard post={mockPostNoImage} />
          </div>
        </section>

        {/* Compact Horizontal */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            2. PostCard Compact - Horizontal
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Layout ngang, phù hợp cho danh sách bài viết trong sidebar hoặc related posts
          </p>
          <div className="space-y-4 max-w-2xl">
            <PostCardCompact post={mockPost} variant="horizontal" />
            <PostCardCompact post={mockPostNoImage} variant="horizontal" />
          </div>
        </section>

        {/* Compact Minimal */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            3. PostCard Compact - Minimal
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Thiết kế tối giản nhất, phù hợp cho widget hoặc khi cần hiển thị nhiều bài viết
          </p>
          <div className="space-y-3 max-w-lg">
            <PostCardCompact post={mockPost} variant="minimal" />
            <PostCardCompact post={mockPostNoImage} variant="minimal" />
          </div>
        </section>

        {/* Grid Comparison */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            4. So sánh trong Grid Layout
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Xem cách các variant hoạt động trong layout grid
          </p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Original cards grid */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                PostCard Gốc (Tối ưu)
              </h3>
              <div className="grid grid-cols-1 gap-4">
                <PostCard post={mockPost} />
                <PostCard post={mockPostNoImage} />
              </div>
            </div>

            {/* Compact cards */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                PostCard Compact
              </h3>
              <div className="space-y-3">
                <PostCardCompact post={mockPost} variant="minimal" />
                <PostCardCompact post={mockPostNoImage} variant="minimal" />
                <PostCardCompact post={mockPost} variant="minimal" />
                <PostCardCompact post={mockPostNoImage} variant="minimal" />
              </div>
            </div>
          </div>
        </section>

        {/* Usage Guide */}
        <section className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Hướng dẫn sử dụng
          </h2>
          <div className="space-y-4 text-gray-600 dark:text-gray-400">
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">PostCard (Tối ưu)</h3>
              <p>Sử dụng cho trang chủ, danh sách bài viết chính. Đã được tối ưu để gọn gàng hơn.</p>
              <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                {'<PostCard post={post} />'}
              </code>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">PostCardCompact - Horizontal</h3>
              <p>Sử dụng cho sidebar, related posts, hoặc khi cần layout ngang.</p>
              <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                {'<PostCardCompact post={post} variant="horizontal" />'}
              </code>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">PostCardCompact - Minimal</h3>
              <p>Sử dụng cho widget, notification, hoặc khi cần hiển thị nhiều bài viết trong không gian nhỏ.</p>
              <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                {'<PostCardCompact post={post} variant="minimal" />'}
              </code>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
