import { apiClient } from './client';
import { apiEndpoints } from '~/utils/api';
import type { Post } from '~/types';

export interface BookmarkResponse {
  posts: Post[];
  total: number;
  page: number;
  limit: number;
}

export interface BookmarkActionResponse {
  success: boolean;
  message: string;
  isBookmarked?: boolean;
}

// Helper function to handle bookmark API errors
const handleBookmarkError = (error: any, action: string) => {
  console.error(`Error ${action}:`, error);
  
  if (error.response?.status === 401) {
    throw new Error('Bạn cần đăng nhập để lưu bài viết');
  } else if (error.response?.status === 404) {
    throw new Error('<PERSON>ài viết không tồn tại');
  } else if (error.response?.status === 405) {
    throw new Error('Chức năng bookmark hiện không khả dụng');
  } else if (error.response?.status === 500) {
    throw new Error('Lỗi server, vui lòng thử lại sau');
  } else {
    throw new Error(error.response?.data?.message || `Không thể ${action}. Vui lòng thử lại.`);
  }
};

export const bookmarksApi = {
  // Get user's bookmarked posts
  getBookmarks: async (page = 0, limit = 10): Promise<BookmarkResponse> => {
    try {
      const response = await apiClient.get(apiEndpoints.bookmarks.list(page, limit));
      return response.data;
    } catch (error) {
      handleBookmarkError(error, 'lấy danh sách');
      throw error;
    }
  },

  // Toggle bookmark - backend uses same endpoint for add/remove
  toggleBookmark: async (postId: string): Promise<BookmarkActionResponse> => {
    try {
      const response = await apiClient.post(apiEndpoints.bookmarks.add(postId));
      return response.data;
    } catch (error) {
      handleBookmarkError(error, 'thay đổi bookmark');
      throw error;
    }
  },

  // Legacy methods for backward compatibility
  addBookmark: async (postId: string): Promise<BookmarkActionResponse> => {
    return bookmarksApi.toggleBookmark(postId);
  },

  removeBookmark: async (postId: string): Promise<BookmarkActionResponse> => {
    return bookmarksApi.toggleBookmark(postId);
  },

  // Check if post is bookmarked
  isBookmarked: async (postId: string): Promise<{ isBookmarked: boolean }> => {
    try {
      const response = await apiClient.get(apiEndpoints.bookmarks.status(postId));
      return response.data;
    } catch (error) {
      handleBookmarkError(error, 'kiểm tra trạng thái');
      throw error;
    }
  }
};
