import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { bookmarksApi } from '~/api/bookmarks';
import { Button } from '~/components/ui/button';
import { useAuthStore } from '~/store/authStore';

export default function BookmarkTest() {
  const { user, isAuthenticated } = useAuthStore();
  const [testPostId, setTestPostId] = useState('62af4add-7b9a-4216-bf7f-735a08380a37');
  const [results, setResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // Test toggle bookmark
  const toggleMutation = useMutation({
    mutationFn: () => bookmarksApi.toggleBookmark(testPostId),
    onSuccess: (data) => {
      addResult(`✅ Toggle thành công: ${JSON.stringify(data)}`);
    },
    onError: (error: any) => {
      addResult(`❌ Toggle thất bại: ${error.message}`);
    }
  });

  // Test get bookmarks
  const { data: bookmarks, refetch: refetchBookmarks } = useQuery({
    queryKey: ['bookmarks'],
    queryFn: () => bookmarksApi.getBookmarks(0, 10),
    enabled: isAuthenticated,
  });

  // Test check bookmark status
  const statusMutation = useMutation({
    mutationFn: () => bookmarksApi.isBookmarked(testPostId),
    onSuccess: (data) => {
      addResult(`✅ Status check: ${JSON.stringify(data)}`);
    },
    onError: (error: any) => {
      addResult(`❌ Status check thất bại: ${error.message}`);
    }
  });

  const clearResults = () => setResults([]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Bookmark API Test</h1>
        
        {!isAuthenticated && (
          <div className="mb-6 p-4 bg-yellow-100 border border-yellow-400 rounded">
            <p>⚠️ Bạn cần đăng nhập để test bookmark API</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Test Controls */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Test Controls</h2>
            
            <div>
              <label className="block text-sm font-medium mb-2">Post ID để test:</label>
              <input
                type="text"
                value={testPostId}
                onChange={(e) => setTestPostId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                placeholder="Nhập Post ID"
              />
            </div>
            
            <div className="space-y-2">
              <Button
                onClick={() => toggleMutation.mutate()}
                disabled={!isAuthenticated || toggleMutation.isPending}
                className="w-full"
              >
                {toggleMutation.isPending ? 'Đang test...' : 'Test Toggle Bookmark'}
              </Button>
              
              <Button
                onClick={() => statusMutation.mutate()}
                disabled={!isAuthenticated || statusMutation.isPending}
                variant="secondary"
                className="w-full"
              >
                {statusMutation.isPending ? 'Đang check...' : 'Check Bookmark Status'}
              </Button>
              
              <Button
                onClick={() => refetchBookmarks()}
                disabled={!isAuthenticated}
                variant="secondary"
                className="w-full"
              >
                Refresh Bookmarks List
              </Button>
              
              <Button
                onClick={clearResults}
                variant="outline"
                className="w-full"
              >
                Clear Results
              </Button>
            </div>
          </div>

          {/* Current State */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Current State</h2>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">User Info:</h3>
              <p className="text-sm">Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
              {user && (
                <p className="text-sm">User: {user.username} ({user.email})</p>
              )}
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Bookmarks Count:</h3>
              <p className="text-sm">
                {bookmarks ? `${bookmarks.total} bookmarks` : 'Loading...'}
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">API Endpoint:</h3>
              <p className="text-xs font-mono break-all">
                POST /api/v1/post/{testPostId}/bookmark
              </p>
            </div>
          </div>
        </div>

        {/* Results Console */}
        <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-white font-bold">Test Results:</h3>
          </div>
          {results.length === 0 ? (
            <p className="text-gray-500">Chưa có kết quả test nào...</p>
          ) : (
            results.map((result, index) => (
              <div key={index} className="mb-1">
                {result}
              </div>
            ))
          )}
        </div>

        {/* API Documentation */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">API Documentation</h3>
          <div className="space-y-2 text-sm">
            <p><strong>Bookmark Toggle:</strong> POST /api/v1/post/{'{postId}'}/bookmark</p>
            <p><strong>Get Bookmarks:</strong> GET /api/v1/post/saved-posts?page=0&limit=10</p>
            <p><strong>Check Status:</strong> GET /api/v1/post/{'{postId}'}/bookmark/status</p>
            <p className="text-blue-700 mt-4">
              <strong>Note:</strong> Backend sử dụng cùng một endpoint cho cả add và remove bookmark (toggle behavior)
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}